package cn.edu.sdu.java.server.models;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Score 模型测试类
 * 主要测试成绩等级自动计算功能
 */
@ExtendWith(MockitoExtension.class)
class ScoreTest {

    private Score score;

    @BeforeEach
    void setUp() {
        score = new Score();
    }

    @Test
    void testCalculateGrade_A等级_90分以上() {
        // 测试A等级 (90-100分)
        score.setScoreValue(90.0);
        // 手动调用calculateGrade方法来模拟@PrePersist/@PreUpdate行为
        invokeCalculateGrade(score);
        assertEquals("A", score.getGrade());

        score.setScoreValue(95.0);
        invokeCalculateGrade(score);
        assertEquals("A", score.getGrade());

        score.setScoreValue(100.0);
        invokeCalculateGrade(score);
        assertEquals("A", score.getGrade());
    }

    @Test
    void testCalculateGrade_B等级_80到89分() {
        // 测试B等级 (80-89分)
        score.setScoreValue(80.0);
        invokeCalculateGrade(score);
        assertEquals("B", score.getGrade());

        score.setScoreValue(85.0);
        invokeCalculateGrade(score);
        assertEquals("B", score.getGrade());

        score.setScoreValue(89.0);
        invokeCalculateGrade(score);
        assertEquals("B", score.getGrade());
    }

    @Test
    void testCalculateGrade_C等级_70到79分() {
        // 测试C等级 (70-79分)
        score.setScoreValue(70.0);
        invokeCalculateGrade(score);
        assertEquals("C", score.getGrade());

        score.setScoreValue(75.0);
        invokeCalculateGrade(score);
        assertEquals("C", score.getGrade());

        score.setScoreValue(79.0);
        invokeCalculateGrade(score);
        assertEquals("C", score.getGrade());
    }

    @Test
    void testCalculateGrade_D等级_60到69分() {
        // 测试D等级 (60-69分)
        score.setScoreValue(60.0);
        invokeCalculateGrade(score);
        assertEquals("D", score.getGrade());

        score.setScoreValue(65.0);
        invokeCalculateGrade(score);
        assertEquals("D", score.getGrade());

        score.setScoreValue(69.0);
        invokeCalculateGrade(score);
        assertEquals("D", score.getGrade());
    }

    @Test
    void testCalculateGrade_F等级_60分以下() {
        // 测试F等级 (0-59分)
        score.setScoreValue(0.0);
        invokeCalculateGrade(score);
        assertEquals("F", score.getGrade());

        score.setScoreValue(30.0);
        invokeCalculateGrade(score);
        assertEquals("F", score.getGrade());

        score.setScoreValue(59.0);
        invokeCalculateGrade(score);
        assertEquals("F", score.getGrade());
    }

    @Test
    void testCalculateGrade_边界值测试() {
        // 测试边界值
        score.setScoreValue(89.9);
        invokeCalculateGrade(score);
        assertEquals("B", score.getGrade());

        score.setScoreValue(90.0);
        invokeCalculateGrade(score);
        assertEquals("A", score.getGrade());

        score.setScoreValue(79.9);
        invokeCalculateGrade(score);
        assertEquals("C", score.getGrade());

        score.setScoreValue(80.0);
        invokeCalculateGrade(score);
        assertEquals("B", score.getGrade());

        score.setScoreValue(69.9);
        invokeCalculateGrade(score);
        assertEquals("D", score.getGrade());

        score.setScoreValue(70.0);
        invokeCalculateGrade(score);
        assertEquals("C", score.getGrade());

        score.setScoreValue(59.9);
        invokeCalculateGrade(score);
        assertEquals("F", score.getGrade());

        score.setScoreValue(60.0);
        invokeCalculateGrade(score);
        assertEquals("D", score.getGrade());
    }

    @Test
    void testCalculateGrade_空值处理() {
        // 测试空值情况
        score.setScoreValue(null);
        invokeCalculateGrade(score);
        assertNull(score.getGrade()); // 当scoreValue为null时，grade应该保持原值（这里是null）
    }

    @Test
    void testCalculateGrade_负数处理() {
        // 测试负数情况
        score.setScoreValue(-10.0);
        invokeCalculateGrade(score);
        assertEquals("F", score.getGrade());
    }

    @Test
    void testCalculateGrade_超过100分处理() {
        // 测试超过100分的情况
        score.setScoreValue(110.0);
        invokeCalculateGrade(score);
        assertEquals("A", score.getGrade());
    }

    @Test
    void testScoreModel_基本属性设置() {
        // 测试Score模型的基本属性设置
        Student student = new Student();
        student.setPersonId(1);

        Course course = new Course();
        course.setCourseId(1);
        course.setName("Java程序设计");

        score.setScoreId(1);
        score.setStudent(student);
        score.setCourse(course);
        score.setScoreValue(85.0);
        score.setGrade("B");

        assertEquals(1, score.getScoreId());
        assertEquals(student, score.getStudent());
        assertEquals(course, score.getCourse());
        assertEquals(85.0, score.getScoreValue());
        assertEquals("B", score.getGrade());
    }

    @Test
    void testScoreModel_完整流程() {
        // 测试完整的成绩创建和等级计算流程
        Student student = new Student();
        student.setPersonId(1);

        Course course = new Course();
        course.setCourseId(1);
        course.setName("数据结构");

        score.setStudent(student);
        score.setCourse(course);
        score.setScoreValue(92.0);
        
        // 模拟持久化前的等级计算
        invokeCalculateGrade(score);

        assertNotNull(score.getStudent());
        assertNotNull(score.getCourse());
        assertEquals(92.0, score.getScoreValue());
        assertEquals("A", score.getGrade());
    }

    /**
     * 通过反射调用私有的calculateGrade方法
     * 用于模拟@PrePersist/@PreUpdate注解的行为
     */
    private void invokeCalculateGrade(Score score) {
        try {
            java.lang.reflect.Method method = Score.class.getDeclaredMethod("calculateGrade");
            method.setAccessible(true);
            method.invoke(score);
        } catch (Exception e) {
            fail("无法调用calculateGrade方法: " + e.getMessage());
        }
    }
}
