package cn.edu.sdu.java.server.services;

import cn.edu.sdu.java.server.payload.request.DataRequest;
import cn.edu.sdu.java.server.payload.response.DataResponse;
import cn.edu.sdu.java.server.payload.response.OptionItemList;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ScoreService 调试测试
 * 用于调试学生和课程选项列表获取问题
 */
@SpringBootTest
@ActiveProfiles("test")
class ScoreServiceDebugTest {

    @Autowired
    private ScoreService scoreService;

    @Test
    void testGetStudentItemOptionList() {
        System.out.println("=== 测试获取学生选项列表 ===");
        
        DataRequest request = new DataRequest();
        OptionItemList result = scoreService.getStudentItemOptionList(request);
        
        assertNotNull(result, "学生选项列表不应为null");
        assertEquals(0, result.getCode(), "返回码应为0");
        assertNotNull(result.getOptionItemList(), "选项列表不应为null");
        
        System.out.println("学生数量: " + result.getOptionItemList().size());
        result.getOptionItemList().forEach(item -> {
            System.out.println("学生: ID=" + item.getId() + 
                             ", Value=" + item.getValue() + 
                             ", Title=" + item.getTitle());
        });
        
        assertTrue(result.getOptionItemList().size() > 0, "应该至少有一个学生");
    }

    @Test
    void testGetCourseItemOptionList() {
        System.out.println("=== 测试获取课程选项列表 ===");
        
        DataRequest request = new DataRequest();
        OptionItemList result = scoreService.getCourseItemOptionList(request);
        
        assertNotNull(result, "课程选项列表不应为null");
        assertEquals(0, result.getCode(), "返回码应为0");
        assertNotNull(result.getOptionItemList(), "选项列表不应为null");
        
        System.out.println("课程数量: " + result.getOptionItemList().size());
        result.getOptionItemList().forEach(item -> {
            System.out.println("课程: ID=" + item.getId() + 
                             ", Value=" + item.getValue() + 
                             ", Title=" + item.getTitle());
        });
        
        assertTrue(result.getOptionItemList().size() > 0, "应该至少有一个课程");
    }

    @Test
    void testGetAllScores() {
        System.out.println("=== 测试获取所有成绩 ===");
        
        DataRequest request = new DataRequest();
        DataResponse result = scoreService.getScoreList(request);
        
        assertNotNull(result, "成绩列表不应为null");
        assertEquals(0, result.getCode(), "返回码应为0");
        
        if (result.getData() != null) {
            System.out.println("成绩数量: " + ((java.util.List<?>) result.getData()).size());
            ((java.util.List<?>) result.getData()).forEach(item -> {
                System.out.println("成绩记录: " + item);
            });
        } else {
            System.out.println("没有成绩数据");
        }
    }

    @Test
    void testScoreOperationsWithDifferentStudents() {
        System.out.println("=== 测试不同学生的成绩操作 ===");
        
        // 首先获取学生列表
        DataRequest request = new DataRequest();
        OptionItemList studentList = scoreService.getStudentItemOptionList(request);
        OptionItemList courseList = scoreService.getCourseItemOptionList(request);
        
        if (studentList.getOptionItemList().size() > 1 && courseList.getOptionItemList().size() > 0) {
            // 测试第二个学生（如果存在）
            var secondStudent = studentList.getOptionItemList().get(1);
            var firstCourse = courseList.getOptionItemList().get(0);
            
            System.out.println("测试学生: " + secondStudent.getTitle());
            System.out.println("测试课程: " + firstCourse.getTitle());
            
            // 尝试添加成绩
            DataRequest addRequest = new DataRequest();
            addRequest.add("studentId", secondStudent.getId());
            addRequest.add("courseId", firstCourse.getId());
            addRequest.add("scoreValue", 88.0);
            
            DataResponse addResult = scoreService.scoreSave(addRequest);
            System.out.println("添加成绩结果: code=" + addResult.getCode() + ", msg=" + addResult.getMsg());
            
            // 查询该学生的成绩
            DataRequest queryRequest = new DataRequest();
            queryRequest.add("studentId", secondStudent.getId());
            
            DataResponse queryResult = scoreService.getScoreList(queryRequest);
            System.out.println("查询学生成绩结果: code=" + queryResult.getCode());
            if (queryResult.getData() != null) {
                System.out.println("该学生成绩数量: " + ((java.util.List<?>) queryResult.getData()).size());
            }
        } else {
            System.out.println("学生或课程数据不足，跳过测试");
        }
    }
}
