package cn.edu.sdu.java.server.models;

import cn.edu.sdu.java.server.repositorys.CourseRepository;
import cn.edu.sdu.java.server.repositorys.PersonRepository;
import cn.edu.sdu.java.server.repositorys.ScoreRepository;
import cn.edu.sdu.java.server.repositorys.StudentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试Score表的外键约束问题
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class ScoreForeignKeyTest {

    @Autowired
    private ScoreRepository scoreRepository;

    @Autowired
    private StudentRepository studentRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private PersonRepository personRepository;

    private Student testStudent;
    private Course testCourse;
    private Person testPerson;

    @BeforeEach
    void setUp() {
        // 清理数据
        scoreRepository.deleteAll();
        studentRepository.deleteAll();
        courseRepository.deleteAll();
        personRepository.deleteAll();

        // 创建测试用的Person
        testPerson = new Person();
        testPerson.setNum("2021001");
        testPerson.setName("张三");
        testPerson.setType("1"); // 学生类型
        testPerson = personRepository.save(testPerson);

        // 创建测试用的Student
        testStudent = new Student();
        testStudent.setPersonId(testPerson.getPersonId());
        testStudent.setPerson(testPerson);
        testStudent.setMajor("计算机科学与技术");
        testStudent.setClassName("计科2021-1班");
        testStudent = studentRepository.save(testStudent);

        // 创建测试用的Course
        testCourse = new Course();
        testCourse.setCourseCode("CS101");
        testCourse.setName("Java程序设计");
        testCourse.setCredit(3.0);
        testCourse.setType("必修");
        testCourse = courseRepository.save(testCourse);
    }

    @Test
    void testCreateScoreWithValidForeignKeys() {
        // 测试使用有效的外键创建成绩记录
        Score score = new Score();
        score.setStudent(testStudent);
        score.setCourse(testCourse);
        score.setScoreValue(85.0);

        // 这应该成功保存
        Score savedScore = scoreRepository.save(score);
        
        assertNotNull(savedScore.getScoreId());
        assertEquals(85.0, savedScore.getScoreValue());
        assertEquals("B", savedScore.getGrade());
        assertEquals(testStudent.getPersonId(), savedScore.getStudent().getPersonId());
        assertEquals(testCourse.getCourseId(), savedScore.getCourse().getCourseId());
    }

    @Test
    void testStudentPersonRelationship() {
        // 测试Student和Person的关联关系
        assertNotNull(testStudent.getPersonId());
        assertNotNull(testStudent.getPerson());
        assertEquals(testPerson.getPersonId(), testStudent.getPersonId());
        assertEquals(testPerson.getPersonId(), testStudent.getPerson().getPersonId());
    }

    @Test
    void testFindStudentById() {
        // 测试通过personId查找Student
        Student foundStudent = studentRepository.findById(testStudent.getPersonId()).orElse(null);
        
        assertNotNull(foundStudent);
        assertEquals(testStudent.getPersonId(), foundStudent.getPersonId());
        assertEquals("张三", foundStudent.getPerson().getName());
        assertEquals("2021001", foundStudent.getPerson().getNum());
    }

    @Test
    void testScoreQueryMethods() {
        // 先创建一个成绩记录
        Score score = new Score();
        score.setStudent(testStudent);
        score.setCourse(testCourse);
        score.setScoreValue(90.0);
        scoreRepository.save(score);

        // 测试各种查询方法
        List<Score> allScores = scoreRepository.findAll();
        assertEquals(1, allScores.size());

        List<Score> studentScores = scoreRepository.findByStudentPersonId(testStudent.getPersonId());
        assertEquals(1, studentScores.size());
        assertEquals(90.0, studentScores.get(0).getScoreValue());

        List<Score> courseScores = scoreRepository.findByCourseCourseId(testCourse.getCourseId());
        assertEquals(1, courseScores.size());

        List<Score> combinedScores = scoreRepository.findByStudentIdAndCourseId(
                testStudent.getPersonId(), testCourse.getCourseId());
        assertEquals(1, combinedScores.size());
    }

    @Test
    void testScoreGradeCalculation() {
        // 测试成绩等级自动计算
        Score score = new Score();
        score.setStudent(testStudent);
        score.setCourse(testCourse);
        score.setScoreValue(92.0);
        
        Score savedScore = scoreRepository.save(score);
        assertEquals("A", savedScore.getGrade());
    }

    @Test
    void testMultipleStudentsAndCourses() {
        // 创建第二个Person和Student
        Person person2 = new Person();
        person2.setNum("2021002");
        person2.setName("李四");
        person2.setType("1");
        person2 = personRepository.save(person2);

        Student student2 = new Student();
        student2.setPersonId(person2.getPersonId());
        student2.setPerson(person2);
        student2.setMajor("软件工程");
        student2.setClassName("软工2021-1班");
        student2 = studentRepository.save(student2);

        // 创建第二个Course
        Course course2 = new Course();
        course2.setCourseCode("CS102");
        course2.setName("数据结构");
        course2.setCredit(4.0);
        course2.setType("必修");
        course2 = courseRepository.save(course2);

        // 创建多个成绩记录
        Score score1 = new Score();
        score1.setStudent(testStudent);
        score1.setCourse(testCourse);
        score1.setScoreValue(85.0);
        scoreRepository.save(score1);

        Score score2 = new Score();
        score2.setStudent(testStudent);
        score2.setCourse(course2);
        score2.setScoreValue(90.0);
        scoreRepository.save(score2);

        Score score3 = new Score();
        score3.setStudent(student2);
        score3.setCourse(testCourse);
        score3.setScoreValue(88.0);
        scoreRepository.save(score3);

        // 验证查询结果
        List<Score> allScores = scoreRepository.findAll();
        assertEquals(3, allScores.size());

        List<Score> student1Scores = scoreRepository.findByStudentPersonId(testStudent.getPersonId());
        assertEquals(2, student1Scores.size());

        List<Score> student2Scores = scoreRepository.findByStudentPersonId(student2.getPersonId());
        assertEquals(1, student2Scores.size());

        List<Score> course1Scores = scoreRepository.findByCourseCourseId(testCourse.getCourseId());
        assertEquals(2, course1Scores.size());
    }
}
