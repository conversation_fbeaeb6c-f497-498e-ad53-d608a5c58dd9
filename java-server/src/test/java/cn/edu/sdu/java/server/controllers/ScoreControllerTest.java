package cn.edu.sdu.java.server.controllers;

import cn.edu.sdu.java.server.payload.request.DataRequest;
import cn.edu.sdu.java.server.payload.response.DataResponse;
import cn.edu.sdu.java.server.services.ScoreService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ScoreController 集成测试类
 * 测试成绩管理相关的HTTP端点
 */
@WebMvcTest(ScoreController.class)
class ScoreControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ScoreService scoreService;

    @Autowired
    private ObjectMapper objectMapper;

    private DataRequest validRequest;
    private DataResponse successResponse;
    private DataResponse errorResponse;

    @BeforeEach
    void setUp() {
        // 准备有效的请求数据
        validRequest = new DataRequest();
        validRequest.add("studentId", 1);
        validRequest.add("courseId", 1);
        validRequest.add("scoreValue", 85.0);

        // 准备成功响应
        successResponse = new DataResponse();
        successResponse.setCode(0);
        successResponse.setMsg("成功");

        // 准备错误响应
        errorResponse = new DataResponse();
        errorResponse.setCode(-1);
        errorResponse.setMsg("该学生在此课程的成绩已存在");
    }

    @Test
    @WithMockUser
    void testScoreSave_成功() throws Exception {
        // 模拟服务层返回成功响应
        when(scoreService.scoreSave(any(DataRequest.class))).thenReturn(successResponse);

        // 执行POST请求并验证响应
        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("成功"));
    }

    @Test
    @WithMockUser
    void testScoreSave_重复成绩错误() throws Exception {
        // 模拟服务层返回错误响应
        when(scoreService.scoreSave(any(DataRequest.class))).thenReturn(errorResponse);

        // 执行POST请求并验证响应
        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.msg").value("该学生在此课程的成绩已存在"));
    }

    @Test
    @WithMockUser
    void testScoreSave_无效JSON格式() throws Exception {
        // 发送无效的JSON格式
        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void testScoreSave_空请求体() throws Exception {
        // 发送空的请求体
        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isOk()); // 根据实际代码，空请求体也会被处理
    }

    @Test
    @WithMockUser
    void testScoreSave_缺少必要参数() throws Exception {
        // 创建缺少参数的请求
        DataRequest incompleteRequest = new DataRequest();
        incompleteRequest.add("studentId", 1);
        // 缺少courseId和scoreValue

        when(scoreService.scoreSave(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(incompleteRequest)))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser
    void testScoreSave_边界值测试() throws Exception {
        // 测试边界值
        DataRequest boundaryRequest = new DataRequest();
        boundaryRequest.add("studentId", 1);
        boundaryRequest.add("courseId", 1);
        boundaryRequest.add("scoreValue", 100.0); // 最高分

        when(scoreService.scoreSave(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(boundaryRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));

        // 测试最低分
        boundaryRequest.add("scoreValue", 0.0);
        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(boundaryRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    @WithMockUser
    void testScoreUpdate_成功() throws Exception {
        // 准备更新请求
        DataRequest updateRequest = new DataRequest();
        updateRequest.add("scoreId", 1);
        updateRequest.add("studentId", 1);
        updateRequest.add("courseId", 1);
        updateRequest.add("scoreValue", 90.0);

        when(scoreService.scoreUpdate(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/score/scoreUpdate")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("成功"));
    }

    @Test
    @WithMockUser
    void testScoreDelete_成功() throws Exception {
        // 准备删除请求
        DataRequest deleteRequest = new DataRequest();
        deleteRequest.add("scoreId", 1);

        when(scoreService.scoreDelete(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/score/scoreDelete")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(deleteRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("成功"));
    }

    @Test
    @WithMockUser
    void testGetScoreList_成功() throws Exception {
        // 准备查询请求
        DataRequest queryRequest = new DataRequest();
        queryRequest.add("studentId", 1);
        queryRequest.add("courseId", 1);

        when(scoreService.getScoreList(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/score/getScoreList")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    void testScoreSave_未认证用户() throws Exception {
        // 测试未认证用户访问
        mockMvc.perform(post("/api/score/scoreSave")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser
    void testScoreSave_不支持的HTTP方法() throws Exception {
        // 测试GET方法（应该不被支持）
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get("/api/score/scoreSave"))
                .andExpect(status().isMethodNotAllowed());
    }

    @Test
    @WithMockUser
    void testScoreSave_不支持的Content_Type() throws Exception {
        // 测试不支持的Content-Type
        mockMvc.perform(post("/api/score/scoreSave")
                        .with(csrf())
                        .contentType(MediaType.TEXT_PLAIN)
                        .content("plain text content"))
                .andExpect(status().isUnsupportedMediaType());
    }
}
