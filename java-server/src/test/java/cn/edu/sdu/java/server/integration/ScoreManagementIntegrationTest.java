package cn.edu.sdu.java.server.integration;

import cn.edu.sdu.java.server.models.Course;
import cn.edu.sdu.java.server.models.Person;
import cn.edu.sdu.java.server.models.Score;
import cn.edu.sdu.java.server.models.Student;
import cn.edu.sdu.java.server.payload.request.DataRequest;
import cn.edu.sdu.java.server.payload.response.DataResponse;
import cn.edu.sdu.java.server.repositorys.CourseRepository;
import cn.edu.sdu.java.server.repositorys.PersonRepository;
import cn.edu.sdu.java.server.repositorys.ScoreRepository;
import cn.edu.sdu.java.server.repositorys.StudentRepository;
import cn.edu.sdu.java.server.services.ScoreService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 成绩管理集成测试
 * 测试完整的成绩添加、修改、查询流程
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class ScoreManagementIntegrationTest {

    @Autowired
    private ScoreService scoreService;

    @Autowired
    private ScoreRepository scoreRepository;

    @Autowired
    private StudentRepository studentRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private PersonRepository personRepository;

    private Student testStudent;
    private Course testCourse;
    private Person testPerson;

    @BeforeEach
    void setUp() {
        // 清理数据
        scoreRepository.deleteAll();
        studentRepository.deleteAll();
        courseRepository.deleteAll();
        personRepository.deleteAll();

        // 创建测试用的Person
        testPerson = new Person();
        testPerson.setNum("2021001");
        testPerson.setName("张三");
        testPerson.setType("1"); // 学生类型
        testPerson = personRepository.save(testPerson);

        // 创建测试用的Student
        testStudent = new Student();
        testStudent.setPersonId(testPerson.getPersonId());
        testStudent.setPerson(testPerson);
        testStudent.setMajor("计算机科学与技术");
        testStudent.setClassName("计科2021-1班");
        testStudent = studentRepository.save(testStudent);

        // 创建测试用的Course
        testCourse = new Course();
        testCourse.setCourseCode("CS101");
        testCourse.setName("Java程序设计");
        testCourse.setCredit(3.0);
        testCourse.setType("必修");
        testCourse = courseRepository.save(testCourse);
    }

    @Test
    void testCompleteScoreManagementFlow() {
        // 1. 测试添加成绩
        DataRequest addRequest = new DataRequest();
        addRequest.add("studentId", testStudent.getPersonId());
        addRequest.add("courseId", testCourse.getCourseId());
        addRequest.add("scoreValue", 85.0);

        DataResponse addResponse = scoreService.scoreSave(addRequest);
        assertEquals(0, addResponse.getCode(), "添加成绩应该成功");

        // 2. 验证成绩已保存
        List<Score> scores = scoreRepository.findAll();
        assertEquals(1, scores.size(), "应该有一条成绩记录");
        
        Score savedScore = scores.get(0);
        assertEquals(85.0, savedScore.getScoreValue(), "成绩值应该正确");
        assertEquals("B", savedScore.getGrade(), "成绩等级应该自动计算为B");

        // 3. 测试查询所有成绩
        DataRequest queryAllRequest = new DataRequest();
        DataResponse queryAllResponse = scoreService.getScoreList(queryAllRequest);
        assertEquals(0, queryAllResponse.getCode(), "查询所有成绩应该成功");
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> allScores = (List<Map<String, Object>>) queryAllResponse.getData();
        assertEquals(1, allScores.size(), "应该返回一条成绩记录");
        
        Map<String, Object> scoreData = allScores.get(0);
        assertEquals("2021001", scoreData.get("studentNum"), "学生学号应该正确");
        assertEquals("张三", scoreData.get("studentName"), "学生姓名应该正确");
        assertEquals("Java程序设计", scoreData.get("courseName"), "课程名称应该正确");
        assertEquals(85.0, scoreData.get("scoreValue"), "成绩值应该正确");
        assertEquals("B", scoreData.get("grade"), "成绩等级应该正确");

        // 4. 测试按学生查询
        DataRequest queryByStudentRequest = new DataRequest();
        queryByStudentRequest.add("studentId", testStudent.getPersonId());
        DataResponse queryByStudentResponse = scoreService.getScoreList(queryByStudentRequest);
        assertEquals(0, queryByStudentResponse.getCode(), "按学生查询应该成功");
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> studentScores = (List<Map<String, Object>>) queryByStudentResponse.getData();
        assertEquals(1, studentScores.size(), "应该返回该学生的一条成绩记录");

        // 5. 测试按课程查询
        DataRequest queryByCourseRequest = new DataRequest();
        queryByCourseRequest.add("courseId", testCourse.getCourseId());
        DataResponse queryByCourseResponse = scoreService.getScoreList(queryByCourseRequest);
        assertEquals(0, queryByCourseResponse.getCode(), "按课程查询应该成功");
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> courseScores = (List<Map<String, Object>>) queryByCourseResponse.getData();
        assertEquals(1, courseScores.size(), "应该返回该课程的一条成绩记录");

        // 6. 测试修改成绩
        DataRequest updateRequest = new DataRequest();
        updateRequest.add("scoreId", savedScore.getScoreId());
        updateRequest.add("studentId", testStudent.getPersonId());
        updateRequest.add("courseId", testCourse.getCourseId());
        updateRequest.add("scoreValue", 92.0);

        DataResponse updateResponse = scoreService.scoreUpdate(updateRequest);
        assertEquals(0, updateResponse.getCode(), "修改成绩应该成功");

        // 7. 验证成绩已修改
        Score updatedScore = scoreRepository.findById(savedScore.getScoreId()).orElse(null);
        assertNotNull(updatedScore, "修改后的成绩应该存在");
        assertEquals(92.0, updatedScore.getScoreValue(), "成绩值应该已更新");
        assertEquals("A", updatedScore.getGrade(), "成绩等级应该自动更新为A");

        // 8. 测试重复添加成绩（应该失败）
        DataRequest duplicateRequest = new DataRequest();
        duplicateRequest.add("studentId", testStudent.getPersonId());
        duplicateRequest.add("courseId", testCourse.getCourseId());
        duplicateRequest.add("scoreValue", 88.0);

        DataResponse duplicateResponse = scoreService.scoreSave(duplicateRequest);
        assertEquals(-1, duplicateResponse.getCode(), "重复添加成绩应该失败");
        assertTrue(duplicateResponse.getMsg().contains("已存在"), "错误信息应该提示已存在");

        // 9. 测试删除成绩
        DataRequest deleteRequest = new DataRequest();
        deleteRequest.add("scoreId", savedScore.getScoreId());

        DataResponse deleteResponse = scoreService.scoreDelete(deleteRequest);
        assertEquals(0, deleteResponse.getCode(), "删除成绩应该成功");

        // 10. 验证成绩已删除
        List<Score> remainingScores = scoreRepository.findAll();
        assertEquals(0, remainingScores.size(), "删除后应该没有成绩记录");
    }

    @Test
    void testScoreGradeCalculation() {
        // 测试不同分数的等级计算
        testScoreGrade(95.0, "A");
        testScoreGrade(90.0, "A");
        testScoreGrade(89.0, "B");
        testScoreGrade(85.0, "B");
        testScoreGrade(80.0, "B");
        testScoreGrade(79.0, "C");
        testScoreGrade(75.0, "C");
        testScoreGrade(70.0, "C");
        testScoreGrade(69.0, "D");
        testScoreGrade(65.0, "D");
        testScoreGrade(60.0, "D");
        testScoreGrade(59.0, "F");
        testScoreGrade(30.0, "F");
        testScoreGrade(0.0, "F");
    }

    private void testScoreGrade(Double scoreValue, String expectedGrade) {
        // 清理之前的数据
        scoreRepository.deleteAll();

        DataRequest request = new DataRequest();
        request.add("studentId", testStudent.getPersonId());
        request.add("courseId", testCourse.getCourseId());
        request.add("scoreValue", scoreValue);

        DataResponse response = scoreService.scoreSave(request);
        assertEquals(0, response.getCode(), "添加成绩应该成功");

        List<Score> scores = scoreRepository.findAll();
        assertEquals(1, scores.size(), "应该有一条成绩记录");
        
        Score score = scores.get(0);
        assertEquals(expectedGrade, score.getGrade(), 
            String.format("分数%.1f应该对应等级%s", scoreValue, expectedGrade));
    }

    @Test
    void testEmptyQuery() {
        // 测试空查询（应该返回所有数据）
        DataRequest emptyRequest = new DataRequest();
        DataResponse response = scoreService.getScoreList(emptyRequest);
        
        assertEquals(0, response.getCode(), "空查询应该成功");
        assertNotNull(response.getData(), "应该返回数据");
    }
}
