package cn.edu.sdu.java.server.controllers;

import cn.edu.sdu.java.server.payload.request.DataRequest;
import cn.edu.sdu.java.server.payload.response.DataResponse;
import cn.edu.sdu.java.server.payload.response.OptionItemList;
import cn.edu.sdu.java.server.services.CourseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * CourseController 测试类
 * 测试课程管理相关的HTTP端点
 */
@WebMvcTest(CourseController.class)
class CourseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CourseService courseService;

    @Autowired
    private ObjectMapper objectMapper;

    private DataRequest validRequest;
    private DataResponse successResponse;
    private DataResponse errorResponse;
    private OptionItemList optionItemList;

    @BeforeEach
    void setUp() {
        // 准备有效的请求数据
        validRequest = new DataRequest();
        validRequest.add("courseCode", "CS101");
        validRequest.add("name", "Java程序设计");
        validRequest.add("credit", 3.0);
        validRequest.add("type", "必修");

        // 准备成功响应
        successResponse = new DataResponse();
        successResponse.setCode(0);
        successResponse.setMsg("成功");

        // 准备错误响应
        errorResponse = new DataResponse();
        errorResponse.setCode(-1);
        errorResponse.setMsg("课序号已存在：CS101");

        // 准备选项列表响应
        optionItemList = new OptionItemList();
        optionItemList.setCode(0);
    }

    @Test
    @WithMockUser
    void testGetCourseItemOptionList_成功() throws Exception {
        // 模拟服务层返回成功响应
        when(courseService.getCourseItemOptionList(any(DataRequest.class))).thenReturn(optionItemList);

        // 执行POST请求并验证响应
        mockMvc.perform(post("/api/course/getCourseItemOptionList")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(new DataRequest())))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    @WithMockUser
    void testGetCourseList_成功() throws Exception {
        // 模拟服务层返回成功响应
        when(courseService.getCourseList(any(DataRequest.class))).thenReturn(successResponse);

        // 执行POST请求并验证响应
        mockMvc.perform(post("/api/course/getCourseList")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("成功"));
    }

    @Test
    @WithMockUser
    void testCourseSave_新增成功() throws Exception {
        // 模拟服务层返回成功响应
        when(courseService.courseSave(any(DataRequest.class))).thenReturn(successResponse);

        // 执行POST请求并验证响应
        mockMvc.perform(post("/api/course/courseSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("成功"));
    }

    @Test
    @WithMockUser
    void testCourseSave_课序号重复错误() throws Exception {
        // 模拟服务层返回错误响应
        when(courseService.courseSave(any(DataRequest.class))).thenReturn(errorResponse);

        // 执行POST请求并验证响应
        mockMvc.perform(post("/api/course/courseSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.msg").value("课序号已存在：CS101"));
    }

    @Test
    @WithMockUser
    void testCourseSave_更新成功() throws Exception {
        // 准备更新请求
        DataRequest updateRequest = new DataRequest();
        updateRequest.add("courseId", 1);
        updateRequest.add("courseCode", "CS101");
        updateRequest.add("name", "Java高级程序设计");
        updateRequest.add("credit", 4.0);
        updateRequest.add("type", "必修");

        when(courseService.courseSave(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/course/courseSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("成功"));
    }

    @Test
    @WithMockUser
    void testCourseDelete_成功() throws Exception {
        // 准备删除请求
        DataRequest deleteRequest = new DataRequest();
        deleteRequest.add("courseId", 1);

        when(courseService.courseDelete(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/course/courseDelete")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(deleteRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("成功"));
    }

    @Test
    @WithMockUser
    void testCourseSave_无效JSON格式() throws Exception {
        // 发送无效的JSON格式
        mockMvc.perform(post("/api/course/courseSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void testCourseSave_空请求体() throws Exception {
        // 发送空的请求体
        when(courseService.courseSave(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/course/courseSave")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser
    void testGetCourseList_带查询条件() throws Exception {
        // 创建带查询条件的请求
        DataRequest queryRequest = new DataRequest();
        queryRequest.add("courseCode", "CS");
        queryRequest.add("name", "Java");
        queryRequest.add("type", "必修");

        when(courseService.getCourseList(any(DataRequest.class))).thenReturn(successResponse);

        mockMvc.perform(post("/api/course/getCourseList")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    void testCourseSave_未认证用户() throws Exception {
        // 测试未认证用户访问
        mockMvc.perform(post("/api/course/courseSave")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser
    void testCourseSave_不支持的HTTP方法() throws Exception {
        // 测试GET方法（应该不被支持）
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get("/api/course/courseSave"))
                .andExpect(status().isMethodNotAllowed());
    }

    @Test
    @WithMockUser
    void testCourseSave_不支持的Content_Type() throws Exception {
        // 测试不支持的Content-Type
        mockMvc.perform(post("/api/course/courseSave")
                        .with(csrf())
                        .contentType(MediaType.TEXT_PLAIN)
                        .content("plain text content"))
                .andExpect(status().isUnsupportedMediaType());
    }

    @Test
    @WithMockUser
    void testCourseDelete_缺少课程ID() throws Exception {
        // 测试删除时缺少课程ID
        DataRequest emptyRequest = new DataRequest();

        DataResponse errorResponse = new DataResponse();
        errorResponse.setCode(-1);
        errorResponse.setMsg("未提供课程ID");

        when(courseService.courseDelete(any(DataRequest.class))).thenReturn(errorResponse);

        mockMvc.perform(post("/api/course/courseDelete")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.msg").value("未提供课程ID"));
    }
}
