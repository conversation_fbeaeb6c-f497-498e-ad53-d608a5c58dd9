package cn.edu.sdu.java.server.controllers;

import cn.edu.sdu.java.server.models.Course;
import cn.edu.sdu.java.server.models.Person;
import cn.edu.sdu.java.server.models.Score;
import cn.edu.sdu.java.server.models.Student;
import cn.edu.sdu.java.server.repositorys.CourseRepository;
import cn.edu.sdu.java.server.repositorys.PersonRepository;
import cn.edu.sdu.java.server.repositorys.ScoreRepository;
import cn.edu.sdu.java.server.repositorys.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 诊断控制器 - 用于排查数据库问题
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/diagnostic")
public class DiagnosticController {

    @Autowired
    private ScoreRepository scoreRepository;

    @Autowired
    private StudentRepository studentRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private PersonRepository personRepository;

    @GetMapping("/check-data")
    public Map<String, Object> checkData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查Person表
            List<Person> persons = personRepository.findAll();
            result.put("personCount", persons.size());
            result.put("persons", persons.stream().limit(5).toList()); // 只显示前5个
            
            // 检查Student表
            List<Student> students = studentRepository.findAll();
            result.put("studentCount", students.size());
            
            // 检查Course表
            List<Course> courses = courseRepository.findAll();
            result.put("courseCount", courses.size());
            
            // 检查Score表
            List<Score> scores = scoreRepository.findAll();
            result.put("scoreCount", scores.size());
            
            // 详细检查学生数据
            for (int i = 0; i < Math.min(3, students.size()); i++) {
                Student s = students.get(i);
                Map<String, Object> studentInfo = new HashMap<>();
                studentInfo.put("personId", s.getPersonId());
                studentInfo.put("major", s.getMajor());
                studentInfo.put("className", s.getClassName());
                
                if (s.getPerson() != null) {
                    studentInfo.put("personNum", s.getPerson().getNum());
                    studentInfo.put("personName", s.getPerson().getName());
                    studentInfo.put("personType", s.getPerson().getType());
                } else {
                    studentInfo.put("personError", "Person关联为null");
                }
                
                result.put("student" + i, studentInfo);
            }
            
            // 详细检查课程数据
            for (int i = 0; i < Math.min(3, courses.size()); i++) {
                Course c = courses.get(i);
                Map<String, Object> courseInfo = new HashMap<>();
                courseInfo.put("courseId", c.getCourseId());
                courseInfo.put("courseCode", c.getCourseCode());
                courseInfo.put("name", c.getName());
                courseInfo.put("credit", c.getCredit());
                courseInfo.put("type", c.getType());
                
                result.put("course" + i, courseInfo);
            }
            
            // 详细检查成绩数据
            for (int i = 0; i < Math.min(3, scores.size()); i++) {
                Score sc = scores.get(i);
                Map<String, Object> scoreInfo = new HashMap<>();
                scoreInfo.put("scoreId", sc.getScoreId());
                scoreInfo.put("scoreValue", sc.getScoreValue());
                scoreInfo.put("grade", sc.getGrade());
                
                if (sc.getStudent() != null) {
                    scoreInfo.put("studentId", sc.getStudent().getPersonId());
                    if (sc.getStudent().getPerson() != null) {
                        scoreInfo.put("studentName", sc.getStudent().getPerson().getName());
                    }
                } else {
                    scoreInfo.put("studentError", "Student关联为null");
                }
                
                if (sc.getCourse() != null) {
                    scoreInfo.put("courseId", sc.getCourse().getCourseId());
                    scoreInfo.put("courseName", sc.getCourse().getName());
                } else {
                    scoreInfo.put("courseError", "Course关联为null");
                }
                
                result.put("score" + i, scoreInfo);
            }
            
            result.put("status", "success");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
            result.put("errorClass", e.getClass().getSimpleName());
        }
        
        return result;
    }

    @GetMapping("/test-queries")
    public Map<String, Object> testQueries() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试基本查询
            List<Score> allScores = scoreRepository.findAll();
            result.put("findAll", allScores.size());
            
            // 测试学生查询
            List<Student> allStudents = studentRepository.findAll();
            if (!allStudents.isEmpty()) {
                Integer firstStudentId = allStudents.get(0).getPersonId();
                List<Score> studentScores = scoreRepository.findByStudentPersonId(firstStudentId);
                result.put("findByStudentPersonId", studentScores.size());
                result.put("testedStudentId", firstStudentId);
            }
            
            // 测试课程查询
            List<Course> allCourses = courseRepository.findAll();
            if (!allCourses.isEmpty()) {
                Integer firstCourseId = allCourses.get(0).getCourseId();
                List<Score> courseScores = scoreRepository.findByCourseCourseId(firstCourseId);
                result.put("findByCourseCourseId", courseScores.size());
                result.put("testedCourseId", firstCourseId);
            }
            
            // 测试组合查询
            if (!allStudents.isEmpty() && !allCourses.isEmpty()) {
                Integer studentId = allStudents.get(0).getPersonId();
                Integer courseId = allCourses.get(0).getCourseId();
                List<Score> combinedScores = scoreRepository.findByStudentIdAndCourseId(studentId, courseId);
                result.put("findByStudentIdAndCourseId", combinedScores.size());
            }
            
            result.put("status", "success");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
            result.put("errorClass", e.getClass().getSimpleName());
            e.printStackTrace();
        }
        
        return result;
    }

    @PostMapping("/create-test-data")
    public Map<String, Object> createTestData() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查是否已有数据
            List<Score> existingScores = scoreRepository.findAll();
            if (!existingScores.isEmpty()) {
                result.put("status", "skipped");
                result.put("message", "已存在成绩数据，跳过创建");
                return result;
            }

            // 获取学生和课程
            List<Student> students = studentRepository.findAll();
            List<Course> courses = courseRepository.findAll();

            if (students.isEmpty() || courses.isEmpty()) {
                result.put("status", "error");
                result.put("message", "缺少学生或课程数据");
                return result;
            }

            // 创建测试成绩数据
            int created = 0;
            for (int i = 0; i < Math.min(3, students.size()); i++) {
                for (int j = 0; j < Math.min(2, courses.size()); j++) {
                    Score score = new Score();
                    score.setStudent(students.get(i));
                    score.setCourse(courses.get(j));
                    score.setScoreValue(80.0 + (i * 5) + (j * 3)); // 生成不同的分数

                    scoreRepository.save(score);
                    created++;
                }
            }

            result.put("status", "success");
            result.put("created", created);

        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
            result.put("errorClass", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }

    @PostMapping("/debug-score-save")
    public Map<String, Object> debugScoreSave(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            Integer studentId = (Integer) request.get("studentId");
            Integer courseId = (Integer) request.get("courseId");
            Double scoreValue = ((Number) request.get("scoreValue")).doubleValue();

            result.put("receivedStudentId", studentId);
            result.put("receivedCourseId", courseId);
            result.put("receivedScoreValue", scoreValue);

            // 检查学生是否存在
            Optional<Student> studentOp = studentRepository.findById(studentId);
            if (studentOp.isPresent()) {
                Student student = studentOp.get();
                result.put("studentFound", true);
                result.put("studentPersonId", student.getPersonId());
                if (student.getPerson() != null) {
                    result.put("studentName", student.getPerson().getName());
                    result.put("studentNum", student.getPerson().getNum());
                } else {
                    result.put("studentPersonNull", true);
                }
            } else {
                result.put("studentFound", false);
            }

            // 检查课程是否存在
            Optional<Course> courseOp = courseRepository.findById(courseId);
            if (courseOp.isPresent()) {
                Course course = courseOp.get();
                result.put("courseFound", true);
                result.put("courseName", course.getName());
                result.put("courseCode", course.getCourseCode());
            } else {
                result.put("courseFound", false);
            }

            // 尝试创建Score对象
            if (studentOp.isPresent() && courseOp.isPresent()) {
                Score score = new Score();
                score.setStudent(studentOp.get());
                score.setCourse(courseOp.get());
                score.setScoreValue(scoreValue);

                result.put("scoreObjectCreated", true);

                // 尝试保存
                try {
                    Score savedScore = scoreRepository.save(score);
                    result.put("scoreSaved", true);
                    result.put("savedScoreId", savedScore.getScoreId());
                    result.put("savedGrade", savedScore.getGrade());
                } catch (Exception saveException) {
                    result.put("scoreSaved", false);
                    result.put("saveError", saveException.getMessage());
                    result.put("saveErrorClass", saveException.getClass().getSimpleName());
                }
            }

            result.put("status", "debug_complete");

        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
            result.put("errorClass", e.getClass().getSimpleName());
            e.printStackTrace();
        }

        return result;
    }
}
