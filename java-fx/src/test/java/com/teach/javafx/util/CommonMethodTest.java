package com.teach.javafx.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

/**
 * CommonMethod 工具类测试
 * 主要测试修复后的getDouble方法
 */
class CommonMethodTest {

    @Test
    void testGetDouble_字符串转换() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", "85.5");
        
        Double result = CommonMethod.getDouble(data, "score");
        assertEquals(85.5, result);
    }

    @Test
    void testGetDouble_整数字符串() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", "90");
        
        Double result = CommonMethod.getDouble(data, "score");
        assertEquals(90.0, result);
    }

    @Test
    void testGetDouble_Double对象() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", 95.5);
        
        Double result = CommonMethod.getDouble(data, "score");
        assertEquals(95.5, result);
    }

    @Test
    void testGetDouble_空值() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", null);
        
        Double result = CommonMethod.getDouble(data, "score");
        assertNull(result);
    }

    @Test
    void testGetDouble_不存在的键() {
        Map<String, Object> data = new HashMap<>();
        
        Double result = CommonMethod.getDouble(data, "nonexistent");
        assertNull(result);
    }

    @Test
    void testGetDouble_无效字符串() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", "invalid");
        
        Double result = CommonMethod.getDouble(data, "score");
        assertNull(result);
    }

    @Test
    void testGetDouble_空字符串() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", "");
        
        Double result = CommonMethod.getDouble(data, "score");
        assertNull(result);
    }

    @Test
    void testGetDouble0_字符串转换() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", "75.5");
        
        Double result = CommonMethod.getDouble0(data, "score");
        assertEquals(75.5, result);
    }

    @Test
    void testGetDouble0_无效字符串返回0() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", "invalid");
        
        Double result = CommonMethod.getDouble0(data, "score");
        assertEquals(0.0, result);
    }

    @Test
    void testGetDouble0_空值返回0() {
        Map<String, Object> data = new HashMap<>();
        data.put("score", null);
        
        Double result = CommonMethod.getDouble0(data, "score");
        assertEquals(0.0, result);
    }

    @Test
    void testGetInteger_字符串转换() {
        Map<String, Object> data = new HashMap<>();
        data.put("id", "123");
        
        Integer result = CommonMethod.getInteger(data, "id");
        assertEquals(123, result);
    }

    @Test
    void testGetInteger_Double字符串() {
        Map<String, Object> data = new HashMap<>();
        data.put("id", "123.7");
        
        Integer result = CommonMethod.getInteger(data, "id");
        assertEquals(123, result); // 应该截断小数部分
    }

    @Test
    void testGetString_正常转换() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "张三");
        
        String result = CommonMethod.getString(data, "name");
        assertEquals("张三", result);
    }

    @Test
    void testGetString_数字转换() {
        Map<String, Object> data = new HashMap<>();
        data.put("id", 123);
        
        String result = CommonMethod.getString(data, "id");
        assertEquals("123", result);
    }

    @Test
    void testGetString_空值返回空字符串() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", null);
        
        String result = CommonMethod.getString(data, "name");
        assertEquals("", result);
    }
}
