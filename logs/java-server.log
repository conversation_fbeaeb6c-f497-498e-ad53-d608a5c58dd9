2025-06-03 12:11:53,328 [background-preinit] INFO  o.h.validator.internal.util.Version                - HV000001: Hibernate Validator 8.0.2.Final
2025-06-03 12:11:53,364 [main] INFO  c.e.s.java.server.JavaServerApplication            - Starting JavaServerApplication using Java 23 with PID 13120 (C:\Users\<USER>\IdeaProjects\baicai\java-server\target\classes started by 邓宇希 in C:\Users\<USER>\IdeaProjects\baicai)
2025-06-03 12:11:53,365 [main] INFO  c.e.s.java.server.JavaServerApplication            - No active profile set, falling back to 1 default profile: "default"
2025-06-03 12:11:53,862 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 12:11:53,864 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-03 12:11:53,986 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 116 ms. Found 16 JPA repository interfaces.
2025-06-03 12:11:53,999 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 12:11:54,000 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 12:11:54,012 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,012 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.DictionaryInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,012 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FamilyMemberRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,012 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FeeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,012 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.MenuInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,012 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ModifyLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,015 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.PersonRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,015 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ScoreRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,015 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StatisticsDayRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.SystemInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.TeacherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.VolunteerRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 12:11:54,016 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-03 12:11:54,402 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat initialized with port 22223 (http)
2025-06-03 12:11:54,412 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Initializing ProtocolHandler ["http-nio-22223"]
2025-06-03 12:11:54,413 [main] INFO  o.apache.catalina.core.StandardService             - Starting service [Tomcat]
2025-06-03 12:11:54,413 [main] INFO  org.apache.catalina.core.StandardEngine            - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-06-03 12:11:54,455 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring embedded WebApplicationContext
2025-06-03 12:11:54,456 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext     - Root WebApplicationContext: initialization completed in 1061 ms
2025-06-03 12:11:54,535 [main] INFO  o.hibernate.jpa.internal.util.LogHelper            - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-03 12:11:54,578 [main] INFO  org.hibernate.Version                              - HHH000412: Hibernate ORM core version 6.6.5.Final
2025-06-03 12:11:54,601 [main] INFO  o.h.c.internal.RegionFactoryInitiator              - HHH000026: Second-level cache disabled
2025-06-03 12:11:54,803 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo                - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-03 12:11:54,823 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Starting...
2025-06-03 12:11:59,477 [main] INFO  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4d75c604
2025-06-03 12:11:59,478 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Start completed.
2025-06-03 12:11:59,543 [main] INFO  org.hibernate.orm.connections.pooling              - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.35
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-03 12:12:00,219 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator                 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-03 12:12:08,609 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-03 12:12:08,787 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory               - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-03 12:12:09,201 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-03 12:12:09,389 [main] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-03 12:12:09,389 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-03 12:12:10,209 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Starting ProtocolHandler ["http-nio-22223"]
2025-06-03 12:12:10,221 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat started on port 22223 (http) with context path '/'
2025-06-03 12:12:10,227 [main] INFO  c.e.s.java.server.JavaServerApplication            - Started JavaServerApplication in 17.241 seconds (process running for 17.87)
2025-06-03 12:12:10,229 [main] INFO  c.e.s.j.server.SystemApplicationListener           - org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@2b4ab7]
2025-06-03 12:12:10,229 [main] INFO  c.e.s.j.server.SystemApplicationListener           - SystemInitStart
2025-06-03 12:12:10,361 [main] INFO  c.e.s.j.server.SystemApplicationListener           - systemInitEnd
2025-06-03 12:13:39,334 [http-nio-22223-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-03 12:13:39,336 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Initializing Servlet 'dispatcherServlet'
2025-06-03 12:13:39,336 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Completed initialization in 0 ms
2025-06-03 12:13:40,002 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 12:13:39,0.241
2025-06-03 12:13:59,213 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 12:13:59,0.044
2025-06-03 12:13:59,255 [http-nio-22223-exec-5] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 12:13:59,281 [http-nio-22223-exec-5] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 12:13:59,283 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 12:13:59,0.059
2025-06-03 12:13:59,558 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 12:13:59,0.044
2025-06-03 12:13:59,600 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 12:13:59,0.032
2025-06-03 12:14:04,408 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getStudentItemOptionList,admin,2025-06-03 12:14:04,0.089
2025-06-03 12:14:04,492 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 12:14:04,0.076
2025-06-03 12:14:04,587 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getVolunteerList,admin,2025-06-03 12:14:04,0.086
2025-06-03 12:14:04,676 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 12:14:04,0.083
2025-06-03 12:22:17,339 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 12:22:17,0.23
2025-06-03 12:22:35,194 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 12:22:35,0.042
2025-06-03 12:22:35,238 [http-nio-22223-exec-5] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 12:22:35,281 [http-nio-22223-exec-5] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 12:22:35,282 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 12:22:35,0.077
2025-06-03 12:22:37,611 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 12:22:37,0.086
2025-06-03 12:22:37,658 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 12:22:37,0.04
2025-06-03 12:22:40,834 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getStudentItemOptionList,admin,2025-06-03 12:22:40,0.067
2025-06-03 12:22:40,909 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 12:22:40,0.069
2025-06-03 12:22:40,984 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getVolunteerList,admin,2025-06-03 12:22:40,0.066
2025-06-03 12:22:41,044 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 12:22:40,0.055
2025-06-03 12:23:46,473 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-03 12:23:46,0.203
2025-06-03 12:23:46,591 [http-nio-22223-exec-3] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\22.jpg (系统找不到指定的路径。)
2025-06-03 12:23:46,591 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-03 12:23:46,0.109
2025-06-03 12:23:52,523 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/studentDelete,admin,2025-06-03 12:23:52,0.413
2025-06-03 12:23:52,603 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 12:23:52,0.055
2025-06-03 12:24:06,539 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 12:24:06,0.032
2025-06-03 12:33:19,782 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 12:33:19,0.204
2025-06-03 12:33:22,272 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 12:33:22,0.059
2025-06-03 12:33:22,317 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 12:33:22,0.037
2025-06-03 12:40:05,630 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 12:40:05,0.206
2025-06-03 12:44:31,569 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 12:44:31,0.193
2025-06-03 12:44:35,772 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 12:44:35,0.052
2025-06-03 12:44:35,820 [http-nio-22223-exec-6] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 12:44:35,865 [http-nio-22223-exec-6] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 12:44:35,867 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 12:44:35,0.084
2025-06-03 12:48:48,798 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 12:48:48,0.196
2025-06-03 13:40:42,106 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m41s230ms988µs400ns).
2025-06-03 13:46:24,616 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 13:46:24,0.167
2025-06-03 13:49:41,792 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 13:49:41,0.206
2025-06-03 13:57:34,192 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m51s926ms777µs900ns).
2025-06-03 14:05:46,710 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@57a62074 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,711 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@369c1d50 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,712 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@38cd4372 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,712 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5e0e719f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,713 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@ca0d346 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,714 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5a00391f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,714 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@80427fe (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,714 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@252b2de8 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,715 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2f1be0e8 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:46,715 [http-nio-22223-exec-3] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7408fcd5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 14:05:51,680 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 14:05:51,0.19
2025-06-03 14:08:53,296 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 14:08:53,0.165
2025-06-03 14:09:01,807 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 14:09:01,0.108
2025-06-03 14:09:01,890 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 14:09:01,0.056
2025-06-03 14:09:01,977 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:09:01,0.077
2025-06-03 14:09:31,669 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 14:09:31,0.181
2025-06-03 14:09:35,132 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 14:09:34,0.147
2025-06-03 14:09:35,240 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 14:09:35,0.099
2025-06-03 14:09:35,353 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:09:35,0.106
2025-06-03 14:10:24,551 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:10:24,0.138
2025-06-03 14:10:27,283 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:10:27,0.104
2025-06-03 14:10:27,510 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:10:27,0.091
2025-06-03 14:13:14,840 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 14:13:14,0.193
2025-06-03 14:13:18,817 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 14:13:18,0.073
2025-06-03 14:13:18,876 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 14:13:18,0.051
2025-06-03 14:13:18,927 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:13:18,0.043
2025-06-03 14:13:31,078 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 14:13:30,0.25
2025-06-03 14:13:31,130 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:13:31,0.041
2025-06-03 14:13:34,019 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 14:13:33,0.059
2025-06-03 14:13:37,660 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 14:13:37,0.14
2025-06-03 14:13:37,748 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 14:13:37,0.075
2025-06-03 14:14:51,878 [http-nio-22223-exec-3] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 14:14:51,878 [http-nio-22223-exec-3] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 14:15:04,675 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:15:04,0.041
2025-06-03 14:15:07,365 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getStudentItemOptionList,admin,2025-06-03 14:15:07,0.141
2025-06-03 14:15:07,456 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 14:15:07,0.08
2025-06-03 14:15:07,559 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getVolunteerList,admin,2025-06-03 14:15:07,0.094
2025-06-03 14:15:07,840 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 14:15:07,0.27
2025-06-03 14:15:15,144 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 14:15:15,0.092
2025-06-03 14:15:15,286 [http-nio-22223-exec-9] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 14:15:15,397 [http-nio-22223-exec-9] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 14:15:15,398 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 14:15:15,0.186
2025-06-03 14:15:31,879 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 14:15:31,0.484
2025-06-03 14:15:38,394 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 14:15:38,0.068
2025-06-03 14:15:38,488 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 14:15:38,0.085
2025-06-03 14:15:38,542 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:15:38,0.045
2025-06-03 14:15:47,160 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 14:15:46,0.224
2025-06-03 14:15:47,210 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:15:47,0.045
2025-06-03 14:17:15,874 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getStudentItemOptionList,admin,2025-06-03 14:17:15,0.055
2025-06-03 14:17:15,961 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 14:17:15,0.079
2025-06-03 14:17:16,022 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getVolunteerList,admin,2025-06-03 14:17:15,0.052
2025-06-03 14:17:16,108 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 14:17:16,0.071
2025-06-03 14:17:30,568 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/volunteerSave,admin,2025-06-03 14:17:30,0.168
2025-06-03 14:17:30,647 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getVolunteerList,admin,2025-06-03 14:17:30,0.068
2025-06-03 14:17:30,736 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 14:17:30,0.083
2025-06-03 14:17:50,043 [http-nio-22223-exec-9] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 14:17:50,044 [http-nio-22223-exec-9] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 14:20:10,623 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 14:20:10,0.229
2025-06-03 14:20:10,668 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:20:10,0.038
2025-06-03 14:26:18,715 [SpringApplicationShutdownHook] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-03 14:26:18,725 [tomcat-shutdown] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Graceful shutdown complete
2025-06-03 14:26:18,752 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-03 14:26:18,754 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown initiated...
2025-06-03 14:26:18,757 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown completed.
2025-06-03 14:34:27,635 [background-preinit] INFO  o.h.validator.internal.util.Version                - HV000001: Hibernate Validator 8.0.2.Final
2025-06-03 14:34:27,677 [main] INFO  c.e.s.java.server.JavaServerApplication            - Starting JavaServerApplication using Java 23 with PID 13412 (C:\Users\<USER>\IdeaProjects\baicai\java-server\target\classes started by 邓宇希 in C:\Users\<USER>\IdeaProjects\baicai)
2025-06-03 14:34:27,678 [main] INFO  c.e.s.java.server.JavaServerApplication            - No active profile set, falling back to 1 default profile: "default"
2025-06-03 14:34:28,293 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 14:34:28,294 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-03 14:34:28,494 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 194 ms. Found 16 JPA repository interfaces.
2025-06-03 14:34:28,552 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 14:34:28,553 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 14:34:28,567 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,567 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.DictionaryInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,567 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FamilyMemberRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,567 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FeeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,567 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.MenuInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,567 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ModifyLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,569 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.PersonRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,569 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ScoreRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,570 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StatisticsDayRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,570 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,570 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,570 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.SystemInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,570 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.TeacherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,571 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,571 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,571 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.VolunteerRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 14:34:28,571 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-03 14:34:28,984 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat initialized with port 22223 (http)
2025-06-03 14:34:28,992 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Initializing ProtocolHandler ["http-nio-22223"]
2025-06-03 14:34:28,993 [main] INFO  o.apache.catalina.core.StandardService             - Starting service [Tomcat]
2025-06-03 14:34:28,993 [main] INFO  org.apache.catalina.core.StandardEngine            - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-06-03 14:34:29,037 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring embedded WebApplicationContext
2025-06-03 14:34:29,038 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext     - Root WebApplicationContext: initialization completed in 1317 ms
2025-06-03 14:34:29,126 [main] INFO  o.hibernate.jpa.internal.util.LogHelper            - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-03 14:34:29,169 [main] INFO  org.hibernate.Version                              - HHH000412: Hibernate ORM core version 6.6.5.Final
2025-06-03 14:34:29,193 [main] INFO  o.h.c.internal.RegionFactoryInitiator              - HHH000026: Second-level cache disabled
2025-06-03 14:34:29,405 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo                - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-03 14:34:29,433 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Starting...
2025-06-03 14:34:34,169 [main] INFO  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@acd3460
2025-06-03 14:34:34,171 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Start completed.
2025-06-03 14:34:34,254 [main] INFO  org.hibernate.orm.connections.pooling              - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.35
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-03 14:34:35,257 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator                 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-03 14:34:35,545 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-03 14:34:35,795 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory               - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-03 14:34:36,449 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-03 14:34:36,725 [main] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-03 14:34:36,725 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-03 14:34:37,958 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Starting ProtocolHandler ["http-nio-22223"]
2025-06-03 14:34:37,975 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat started on port 22223 (http) with context path '/'
2025-06-03 14:34:37,982 [main] INFO  c.e.s.java.server.JavaServerApplication            - Started JavaServerApplication in 10.794 seconds (process running for 11.429)
2025-06-03 14:34:37,983 [main] INFO  c.e.s.j.server.SystemApplicationListener           - org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@7adb0c4f]
2025-06-03 14:34:37,984 [main] INFO  c.e.s.j.server.SystemApplicationListener           - SystemInitStart
2025-06-03 14:34:38,246 [main] INFO  c.e.s.j.server.SystemApplicationListener           - systemInitEnd
2025-06-03 14:34:57,584 [http-nio-22223-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-03 14:34:57,584 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Initializing Servlet 'dispatcherServlet'
2025-06-03 14:34:57,585 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Completed initialization in 1 ms
2025-06-03 14:34:58,688 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 14:34:58,0.514
2025-06-03 14:35:05,752 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 14:35:05,0.165
2025-06-03 14:35:05,881 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 14:35:05,0.108
2025-06-03 14:35:05,965 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:35:05,0.072
2025-06-03 14:35:30,037 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 14:35:29,0.153
2025-06-03 14:35:36,570 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 14:35:36,0.074
2025-06-03 14:51:50,407 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 14:51:50,0.11
2025-06-03 14:51:50,475 [http-nio-22223-exec-4] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 14:51:50,570 [http-nio-22223-exec-4] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 14:51:50,573 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 14:51:50,0.157
2025-06-03 14:51:53,897 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 14:51:53,0.075
2025-06-03 14:51:53,952 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 14:51:53,0.046
2025-06-03 14:55:12,796 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getStudentItemOptionList,admin,2025-06-03 14:55:12,0.356
2025-06-03 14:55:13,131 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 14:55:12,0.328
2025-06-03 14:55:13,329 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getVolunteerList,admin,2025-06-03 14:55:13,0.19
2025-06-03 14:55:13,507 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 14:55:13,0.171
2025-06-03 15:01:07,717 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:01:06,0.779
2025-06-03 15:01:11,193 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:01:10,0.347
2025-06-03 15:01:11,308 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:01:11,0.085
2025-06-03 15:01:23,455 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 15:01:23,0.402
2025-06-03 15:01:28,471 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:01:28,0.098
2025-06-03 15:01:39,182 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreDelete,admin,2025-06-03 15:01:38,0.214
2025-06-03 15:01:39,256 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:01:39,0.066
2025-06-03 15:01:42,725 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreDelete,admin,2025-06-03 15:01:42,0.194
2025-06-03 15:01:42,793 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:01:42,0.06
2025-06-03 15:01:46,095 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreDelete,admin,2025-06-03 15:01:45,0.183
2025-06-03 15:01:46,158 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:01:46,0.056
2025-06-03 15:02:15,257 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:02:15,0.068
2025-06-03 15:02:28,380 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 15:02:28,0.209
2025-06-03 15:02:28,449 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:02:28,0.06
2025-06-03 15:08:31,832 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:08:31,0.217
2025-06-03 15:08:36,592 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:08:36,0.133
2025-06-03 15:08:36,690 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:08:36,0.088
2025-06-03 15:08:41,343 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:08:41,0.077
2025-06-03 15:09:04,218 [http-nio-22223-exec-10] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:09:04,218 [http-nio-22223-exec-10] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:09:07,047 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:09:06,0.065
2025-06-03 15:09:08,260 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:09:08,0.135
2025-06-03 15:09:26,194 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:09:26,0.123
2025-06-03 15:09:40,223 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 15:09:39,0.355
2025-06-03 15:09:40,354 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:09:40,0.125
2025-06-03 15:09:49,430 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreDelete,admin,2025-06-03 15:09:49,0.177
2025-06-03 15:09:49,497 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:09:49,0.056
2025-06-03 15:10:02,297 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreDelete,admin,2025-06-03 15:10:02,0.162
2025-06-03 15:10:02,349 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:02,0.045
2025-06-03 15:10:33,535 [http-nio-22223-exec-4] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:10:33,535 [http-nio-22223-exec-4] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:10:37,209 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:37,0.03
2025-06-03 15:10:37,834 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:37,0.035
2025-06-03 15:10:38,034 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:38,0.034
2025-06-03 15:10:38,221 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:38,0.034
2025-06-03 15:10:38,417 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:38,0.037
2025-06-03 15:10:38,582 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:38,0.032
2025-06-03 15:10:38,802 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:38,0.048
2025-06-03 15:10:41,422 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:41,0.054
2025-06-03 15:10:41,586 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:41,0.047
2025-06-03 15:10:41,754 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:10:41,0.044
2025-06-03 15:12:18,699 [http-nio-22223-exec-8] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:12:18,699 [http-nio-22223-exec-8] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:12:20,419 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:12:20,0.041
2025-06-03 15:12:24,704 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:12:24,0.046
2025-06-03 15:12:27,199 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:12:27,0.074
2025-06-03 15:15:52,514 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:15:52,0.44
2025-06-03 15:15:56,188 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:15:56,0.054
2025-06-03 15:15:56,232 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:15:56,0.036
2025-06-03 15:16:07,838 [http-nio-22223-exec-9] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:16:07,838 [http-nio-22223-exec-9] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:16:11,614 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:16:11,0.128
2025-06-03 15:16:13,870 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:16:13,0.073
2025-06-03 15:16:25,218 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreUpdate,admin,2025-06-03 15:16:24,0.236
2025-06-03 15:16:29,997 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:16:29,0.046
2025-06-03 15:16:30,255 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:16:30,0.073
2025-06-03 15:18:32,353 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 15:18:32,0.112
2025-06-03 15:18:32,387 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 15:18:32,0.025
2025-06-03 15:18:35,515 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-03 15:18:35,0.14
2025-06-03 15:18:35,591 [http-nio-22223-exec-8] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\3.jpg (系统找不到指定的路径。)
2025-06-03 15:18:35,593 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-03 15:18:35,0.067
2025-06-03 15:18:42,132 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 15:18:42,0.06
2025-06-03 15:18:42,177 [http-nio-22223-exec-3] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 15:18:42,220 [http-nio-22223-exec-3] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 15:18:42,221 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 15:18:42,0.076
2025-06-03 15:20:42,050 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/studentEditSave,admin,2025-06-03 15:20:40,1.218
2025-06-03 15:20:42,176 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 15:20:42,0.049
2025-06-03 15:20:53,784 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:20:53,0.055
2025-06-03 15:20:58,128 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-03 15:20:58,0.125
2025-06-03 15:20:58,173 [http-nio-22223-exec-10] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\59.jpg (系统找不到指定的路径。)
2025-06-03 15:20:58,174 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-03 15:20:58,0.037
2025-06-03 15:21:16,363 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:21:15,0.416
2025-06-03 15:21:21,125 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:21:21,0.053
2025-06-03 15:21:21,156 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:21:21,0.023
2025-06-03 15:21:30,666 [http-nio-22223-exec-3] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:21:30,666 [http-nio-22223-exec-3] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:21:35,466 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:21:35,0.061
2025-06-03 15:21:36,315 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:21:36,0.058
2025-06-03 15:21:36,542 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:21:36,0.036
2025-06-03 15:21:40,156 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 15:21:40,0.053
2025-06-03 15:21:40,215 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 15:21:40,0.05
2025-06-03 15:21:52,914 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:21:52,0.079
2025-06-03 15:26:31,175 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:26:31,0.144
2025-06-03 15:26:35,412 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:26:35,0.061
2025-06-03 15:26:35,454 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:26:35,0.034
2025-06-03 15:26:45,106 [http-nio-22223-exec-7] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:26:45,106 [http-nio-22223-exec-7] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:26:47,642 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:26:47,0.043
2025-06-03 15:26:51,193 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:26:51,0.152
2025-06-03 15:27:01,402 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 15:27:01,0.377
2025-06-03 15:27:03,893 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:27:03,0.064
2025-06-03 15:27:17,466 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 15:27:17,0.069
2025-06-03 15:27:17,516 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 15:27:17,0.04
2025-06-03 15:27:26,804 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-03 15:27:26,0.071
2025-06-03 15:27:26,841 [http-nio-22223-exec-9] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\59.jpg (系统找不到指定的路径。)
2025-06-03 15:27:26,842 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-03 15:27:26,0.028
2025-06-03 15:27:40,134 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/studentEditSave,admin,2025-06-03 15:27:39,0.164
2025-06-03 15:27:46,660 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/studentEditSave,admin,2025-06-03 15:27:46,0.357
2025-06-03 15:27:46,815 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 15:27:46,0.118
2025-06-03 15:27:59,770 [http-nio-22223-exec-1] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:27:59,770 [http-nio-22223-exec-1] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:28:05,779 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:28:05,0.046
2025-06-03 15:28:11,192 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-03 15:28:11,0.055
2025-06-03 15:28:11,233 [http-nio-22223-exec-6] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\59.jpg (系统找不到指定的路径。)
2025-06-03 15:28:11,234 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-03 15:28:11,0.032
2025-06-03 15:28:12,172 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:28:12,0.05
2025-06-03 15:28:13,016 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 15:28:12,0.049
2025-06-03 15:28:13,059 [http-nio-22223-exec-1] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 15:28:13,116 [http-nio-22223-exec-1] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 15:28:13,118 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 15:28:13,0.091
2025-06-03 15:28:13,854 [http-nio-22223-exec-5] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师详细信息，personId: 55
2025-06-03 15:28:13,872 [http-nio-22223-exec-5] INFO  c.e.s.j.s.controllers.TeacherController            - 教师详细信息获取成功，personId: 55
2025-06-03 15:28:13,873 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherInfo,admin,2025-06-03 15:28:13,0.06
2025-06-03 15:28:13,961 [http-nio-22223-exec-3] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\55.jpg (系统找不到指定的路径。)
2025-06-03 15:28:13,961 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-03 15:28:13,0.068
2025-06-03 15:28:14,348 [http-nio-22223-exec-7] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师详细信息，personId: 56
2025-06-03 15:28:14,390 [http-nio-22223-exec-7] INFO  c.e.s.j.s.controllers.TeacherController            - 教师详细信息获取成功，personId: 56
2025-06-03 15:28:14,392 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherInfo,admin,2025-06-03 15:28:14,0.101
2025-06-03 15:28:14,454 [http-nio-22223-exec-6] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\56.jpg (系统找不到指定的路径。)
2025-06-03 15:28:14,456 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-03 15:28:14,0.056
2025-06-03 15:28:31,894 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:28:31,0.146
2025-06-03 15:28:34,521 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 15:28:34,0.033
2025-06-03 15:28:34,555 [http-nio-22223-exec-3] INFO  c.e.s.j.s.controllers.TeacherController            - 获取教师列表，查询条件: 
2025-06-03 15:28:34,592 [http-nio-22223-exec-3] INFO  c.e.s.j.s.controllers.TeacherController            - 教师列表查询成功，返回 4 条记录
2025-06-03 15:28:34,594 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-03 15:28:34,0.061
2025-06-03 15:28:39,571 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 15:28:39,0.058
2025-06-03 15:28:39,610 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-03 15:28:39,0.032
2025-06-03 15:28:42,286 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getStudentItemOptionList,admin,2025-06-03 15:28:42,0.075
2025-06-03 15:28:42,347 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 15:28:42,0.053
2025-06-03 15:28:42,406 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getVolunteerList,admin,2025-06-03 15:28:42,0.05
2025-06-03 15:28:42,481 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/volunteer/getActivityItemOptionList,admin,2025-06-03 15:28:42,0.069
2025-06-03 15:28:45,181 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:28:45,0.053
2025-06-03 15:28:45,238 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:28:45,0.049
2025-06-03 15:28:54,036 [http-nio-22223-exec-3] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:28:54,036 [http-nio-22223-exec-3] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:29:01,968 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:29:01,0.055
2025-06-03 15:29:05,026 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:29:04,0.091
2025-06-03 15:29:05,606 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:29:05,0.087
2025-06-03 15:29:42,981 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:29:42,0.117
2025-06-03 15:32:41,084 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/studentEditSave,admin,2025-06-03 15:32:39,1.297
2025-06-03 15:32:41,207 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-03 15:32:41,0.071
2025-06-03 15:32:52,178 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:32:52,0.136
2025-06-03 15:32:56,461 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:32:56,0.055
2025-06-03 15:32:56,496 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:32:56,0.027
2025-06-03 15:33:08,101 [http-nio-22223-exec-8] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:33:08,102 [http-nio-22223-exec-8] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 15:33:11,751 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:33:11,0.052
2025-06-03 15:33:15,132 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:33:15,0.037
2025-06-03 15:45:01,267 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:45:01,0.215
2025-06-03 15:45:04,887 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:45:04,0.039
2025-06-03 15:45:19,294 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:45:19,0.081
2025-06-03 15:45:44,138 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/courseSave,admin,2025-06-03 15:45:44,0.134
2025-06-03 15:45:44,233 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:45:44,0.038
2025-06-03 15:45:54,055 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:45:54,0.052
2025-06-03 15:46:00,991 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:46:00,0.055
2025-06-03 15:46:02,919 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:46:02,0.106
2025-06-03 15:46:02,977 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:46:02,0.048
2025-06-03 15:46:10,211 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:46:10,0.042
2025-06-03 15:46:21,290 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/courseSave,admin,2025-06-03 15:46:21,0.131
2025-06-03 15:46:21,362 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:46:21,0.038
2025-06-03 15:46:26,188 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/courseSave,admin,2025-06-03 15:46:26,0.173
2025-06-03 15:46:26,286 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:46:26,0.073
2025-06-03 15:46:46,668 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/courseSave,admin,2025-06-03 15:46:46,0.201
2025-06-03 15:46:46,721 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:46:46,0.029
2025-06-03 15:48:35,684 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-03 15:48:35,0.045
2025-06-03 15:53:52,324 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m46s855ms35µs100ns).
2025-06-03 15:54:11,324 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1ed87fb5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,325 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6d9c7ce6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,326 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@16b6aa6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,326 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5c9388e9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,326 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@67b8aee5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,327 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@69cd95b8 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,327 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3329fc7d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,328 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@733dfe31 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,328 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5150b935 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:11,329 [http-nio-22223-exec-4] WARN  com.zaxxer.hikari.pool.PoolBase                    - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@543596f2 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-03 15:54:16,655 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 15:54:16,0.427
2025-06-03 15:54:44,152 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 15:54:44,0.146
2025-06-03 15:54:44,237 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 15:54:44,0.072
2025-06-03 15:54:56,646 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 15:54:56,0.147
2025-06-03 15:55:07,225 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/scoreSave,admin,2025-06-03 15:55:06,0.72
2025-06-03 15:55:11,788 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 15:55:11,0.118
2025-06-03 15:55:31,810 [http-nio-22223-exec-5] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 15:55:31,810 [http-nio-22223-exec-5] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 16:02:38,418 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 16:02:38,0.372
2025-06-03 16:02:43,149 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 16:02:42,0.322
2025-06-03 16:02:43,233 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 16:02:43,0.069
2025-06-03 16:03:28,548 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 16:03:28,0.393
2025-06-03 16:03:48,717 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 16:03:48,0.151
2025-06-03 16:03:48,796 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 16:03:48,0.064
2025-06-03 16:04:51,592 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 16:04:51,0.456
2025-06-03 16:04:55,509 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 16:04:55,0.142
2025-06-03 16:04:55,581 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 16:04:55,0.061
2025-06-03 16:06:14,767 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 16:06:14,0.698
2025-06-03 16:06:18,836 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 16:06:18,0.172
2025-06-03 16:06:18,953 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 16:06:18,0.107
2025-06-03 16:12:35,012 [http-nio-22223-exec-4] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 16:12:35,012 [http-nio-22223-exec-4] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 16:12:35,166 [SpringApplicationShutdownHook] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-03 16:12:35,178 [tomcat-shutdown] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Graceful shutdown complete
2025-06-03 16:12:35,250 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-03 16:12:35,262 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown initiated...
2025-06-03 16:12:35,266 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown completed.
2025-06-03 16:12:47,967 [background-preinit] INFO  o.h.validator.internal.util.Version                - HV000001: Hibernate Validator 8.0.2.Final
2025-06-03 16:12:48,023 [main] INFO  c.e.s.java.server.JavaServerApplication            - Starting JavaServerApplication using Java 23 with PID 12708 (C:\Users\<USER>\IdeaProjects\baicai\java-server\target\classes started by 邓宇希 in C:\Users\<USER>\IdeaProjects\baicai)
2025-06-03 16:12:48,024 [main] INFO  c.e.s.java.server.JavaServerApplication            - No active profile set, falling back to 1 default profile: "default"
2025-06-03 16:12:48,703 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 16:12:48,704 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-03 16:12:48,844 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 125 ms. Found 16 JPA repository interfaces.
2025-06-03 16:12:48,871 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 16:12:48,872 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 16:12:48,886 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,886 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.DictionaryInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,886 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FamilyMemberRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,887 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FeeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,887 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.MenuInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,887 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ModifyLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,889 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.PersonRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,889 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ScoreRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,889 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StatisticsDayRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,889 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,890 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,890 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.SystemInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,890 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.TeacherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,890 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,890 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,892 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.VolunteerRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-03 16:12:48,892 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-03 16:12:49,357 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat initialized with port 22223 (http)
2025-06-03 16:12:49,366 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Initializing ProtocolHandler ["http-nio-22223"]
2025-06-03 16:12:49,367 [main] INFO  o.apache.catalina.core.StandardService             - Starting service [Tomcat]
2025-06-03 16:12:49,368 [main] INFO  org.apache.catalina.core.StandardEngine            - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-06-03 16:12:49,421 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring embedded WebApplicationContext
2025-06-03 16:12:49,421 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext     - Root WebApplicationContext: initialization completed in 1354 ms
2025-06-03 16:12:49,525 [main] INFO  o.hibernate.jpa.internal.util.LogHelper            - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-03 16:12:49,580 [main] INFO  org.hibernate.Version                              - HHH000412: Hibernate ORM core version 6.6.5.Final
2025-06-03 16:12:49,612 [main] INFO  o.h.c.internal.RegionFactoryInitiator              - HHH000026: Second-level cache disabled
2025-06-03 16:12:49,837 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo                - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-03 16:12:49,860 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Starting...
2025-06-03 16:12:54,561 [main] INFO  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@14b5325f
2025-06-03 16:12:54,563 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Start completed.
2025-06-03 16:12:54,670 [main] INFO  org.hibernate.orm.connections.pooling              - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.35
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-03 16:12:56,018 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator                 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-03 16:12:59,304 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-03 16:12:59,488 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory               - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-03 16:13:00,182 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-03 16:13:00,440 [main] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-03 16:13:00,440 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-03 16:13:01,645 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Starting ProtocolHandler ["http-nio-22223"]
2025-06-03 16:13:01,669 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat started on port 22223 (http) with context path '/'
2025-06-03 16:13:01,680 [main] INFO  c.e.s.java.server.JavaServerApplication            - Started JavaServerApplication in 14.117 seconds (process running for 14.653)
2025-06-03 16:13:01,682 [main] INFO  c.e.s.j.server.SystemApplicationListener           - org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@5647e00b]
2025-06-03 16:13:01,682 [main] INFO  c.e.s.j.server.SystemApplicationListener           - SystemInitStart
2025-06-03 16:13:01,915 [main] INFO  c.e.s.j.server.SystemApplicationListener           - systemInitEnd
2025-06-03 16:13:04,177 [http-nio-22223-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-03 16:13:04,178 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Initializing Servlet 'dispatcherServlet'
2025-06-03 16:13:04,178 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Completed initialization in 0 ms
2025-06-03 16:13:05,216 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-03 16:13:04,0.471
2025-06-03 16:13:09,530 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-03 16:13:09,0.089
2025-06-03 16:13:09,586 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-03 16:13:09,0.047
2025-06-03 16:13:09,795 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-03 16:13:09,0.101
2025-06-03 16:13:33,915 [http-nio-22223-exec-9] WARN  o.h.engine.jdbc.spi.SqlExceptionHelper             - SQL Error: 1452, SQLState: 23000
2025-06-03 16:13:33,915 [http-nio-22223-exec-9] ERROR o.h.engine.jdbc.spi.SqlExceptionHelper             - Cannot add or update a child row: a foreign key constraint fails (`java_2_21`.`score`, CONSTRAINT `FKnap51mbove93yjb09idc9jic6` FOREIGN KEY (`student_id`) REFERENCES `student` (`student_id`))
2025-06-03 16:14:26,325 [SpringApplicationShutdownHook] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-03 16:14:26,334 [tomcat-shutdown] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Graceful shutdown complete
2025-06-03 16:14:26,486 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-03 16:14:26,489 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown initiated...
2025-06-03 16:14:26,498 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown completed.
